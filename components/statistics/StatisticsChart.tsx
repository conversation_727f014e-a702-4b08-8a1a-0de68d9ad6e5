import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { BarChart } from 'react-native-chart-kit';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

interface ChartData {
  day: string;
  completed: number;
}

interface StatisticsChartProps {
  data: ChartData[];
  title: string;
  description?: string;
}

export function StatisticsChart({ data, title, description }: StatisticsChartProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const screenWidth = Dimensions.get('window').width - 32; // Padding
  
  const chartData = {
    labels: data.map(item => item.day),
    datasets: [
      {
        data: data.map(item => item.completed),
      },
    ],
  };
  
  const chartConfig = {
    backgroundGradientFrom: colorScheme === 'dark' ? '#1E1E1E' : '#FFFFFF',
    backgroundGradientTo: colorScheme === 'dark' ? '#1E1E1E' : '#FFFFFF',
    decimalPlaces: 1,
    color: (opacity = 1) => {
      return colorScheme === 'dark' 
        ? `rgba(255, 255, 255, ${opacity})` 
        : `rgba(10, 126, 164, ${opacity})`;
    },
    labelColor: (opacity = 1) => {
      return colorScheme === 'dark' 
        ? `rgba(255, 255, 255, ${opacity})` 
        : `rgba(0, 0, 0, ${opacity})`;
    },
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: colors.tint,
    },
    barPercentage: 0.6,
  };
  
  return (
    <ThemedView style={styles.container}>
      <ThemedText type="defaultSemiBold" style={styles.title}>
        {title}
      </ThemedText>
      
      {description && (
        <ThemedText style={styles.description}>
          {description}
        </ThemedText>
      )}
      
      <View style={styles.chartContainer}>
        <BarChart
          data={chartData}
          width={screenWidth}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          fromZero
          showValuesOnTopOfBars
          withInnerLines={false}
        />
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 16,
    opacity: 0.8,
  },
  chartContainer: {
    alignItems: 'center',
  },
  chart: {
    borderRadius: 16,
    paddingRight: 0,
  },
});
