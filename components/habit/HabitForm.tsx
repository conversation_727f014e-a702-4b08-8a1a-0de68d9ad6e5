import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  Pressable
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { format } from 'date-fns';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Habit, HabitCategory, HabitFrequency, HabitWeekdays } from '@/types/habit';
import { HABIT_CATEGORIES } from '@/constants/HabitCategories';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

// Define habit icons
interface HabitIcon {
  id: string;
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  color: string;
  bgColor: string;
}

const HABIT_ICONS: HabitIcon[] = [
  { id: 'run', name: 'Run', icon: 'directions_run', color: '#EF4444', bgColor: '#FEE2E2' },
  { id: 'meditate', name: 'Meditate', icon: 'self_improvement', color: '#10B981', bgColor: '#ECFDF5' },
  { id: 'read', name: 'Read', icon: 'book', color: '#F59E0B', bgColor: '#FFFBEB' },
  { id: 'water', name: 'Water', icon: 'water_drop', color: '#3B82F6', bgColor: '#EFF6FF' },
  { id: 'workout', name: 'Workout', icon: 'fitness_center', color: '#EF4444', bgColor: '#FEE2E2' },
  { id: 'journal', name: 'Journal', icon: 'draw', color: '#8B5CF6', bgColor: '#F5F3FF' },
  { id: 'code', name: 'Code', icon: 'code', color: '#0EA5E9', bgColor: '#E0F2FE' },
  { id: 'healthy', name: 'Healthy', icon: 'emoji_food_beverage', color: '#059669', bgColor: '#D1FAE5' },
  { id: 'learn', name: 'Learn', icon: 'lightbulb', color: '#F97316', bgColor: '#FFF7ED' },
  { id: 'custom', name: 'Custom', icon: 'more_horiz', color: '#0284C7', bgColor: '#F0F9FF' },
];

interface HabitFormProps {
  initialValues?: Partial<Habit>;
  onSubmit: (habit: Omit<Habit, 'id' | 'createdAt' | 'completions'>) => Promise<void>;
  isEditing?: boolean;
}

export function HabitForm({ initialValues, onSubmit, isEditing = false }: HabitFormProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  // Form state
  const [name, setName] = useState(initialValues?.name || '');
  const [description, setDescription] = useState(initialValues?.description || '');
  const [category, setCategory] = useState<HabitCategory>(initialValues?.category || 'productivity');
  const [selectedIcon, setSelectedIcon] = useState<string>(initialValues?.icon || 'directions_run');
  const [frequency, setFrequency] = useState<HabitFrequency>(initialValues?.frequency || 'daily');
  const [timeOfDay, setTimeOfDay] = useState<string>(initialValues?.reminderTime ? 'specific' : 'anytime');
  const [weekdays, setWeekdays] = useState<HabitWeekdays>(initialValues?.weekdays || {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: true,
    sunday: true,
  });
  const [targetDays, setTargetDays] = useState<number>(initialValues?.targetDays || 1);
  const [reminderEnabled, setReminderEnabled] = useState(!!initialValues?.reminderTime);
  const [reminderTime, setReminderTime] = useState(initialValues?.reminderTime || '20:00');
  const [startDate, setStartDate] = useState(initialValues?.startDate || new Date().toISOString());
  const [endDate, setEndDate] = useState(initialValues?.endDate || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation
  const [nameError, setNameError] = useState('');

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!name.trim()) {
      setNameError('Habit name is required');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    setIsSubmitting(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    try {
      await onSubmit({
        name,
        description,
        category,
        icon: selectedIcon,
        frequency,
        weekdays: frequency === 'custom' ? weekdays : undefined,
        targetDays: frequency === 'custom' ? targetDays : undefined,
        startDate,
        endDate: endDate || undefined,
        reminderTime: reminderEnabled ? reminderTime : undefined,
        archived: initialValues?.archived || false,
      });

      router.back();
    } catch (error) {
      console.error('Error submitting habit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Toggle a weekday
  const toggleWeekday = (day: keyof HabitWeekdays) => {
    setWeekdays(prev => ({
      ...prev,
      [day]: !prev[day],
    }));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.mainContainer}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Habit Name */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Habit Name</ThemedText>
            <View style={styles.inputContainer}>
              <MaterialIcons name="edit_note" size={20} color="#94A3B8" style={styles.inputIcon} />
              <TextInput
                style={[
                  styles.input,
                  { color: colors.text },
                ]}
                placeholder="e.g. Morning Meditation"
                placeholderTextColor="#94A3B8"
                value={name}
                onChangeText={(text) => {
                  setName(text);
                  if (text.trim()) setNameError('');
                }}
              />
            </View>
            {nameError ? (
              <ThemedText style={styles.errorText}>{nameError}</ThemedText>
            ) : null}
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              Description <ThemedText style={styles.optionalText}>(Optional)</ThemedText>
            </ThemedText>
            <TextInput
              style={[
                styles.textArea,
                { color: colors.text },
              ]}
              placeholder="e.g. 10 minutes of guided meditation using Calm app"
              placeholderTextColor="#94A3B8"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.sectionTitle}>Choose an Icon</ThemedText>
            <View style={styles.iconGrid}>
              {HABIT_ICONS.map((icon) => (
                <TouchableOpacity
                  key={icon.id}
                  style={[
                    styles.iconItem,
                    { backgroundColor: icon.bgColor },
                    selectedIcon === icon.icon && { backgroundColor: icon.color }
                  ]}
                  onPress={() => {
                    setSelectedIcon(icon.icon);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  <MaterialIcons
                    name={icon.icon}
                    size={28}
                    color={selectedIcon === icon.icon ? '#FFFFFF' : icon.color}
                    style={{ marginBottom: 4 }}
                  />
                  <ThemedText
                    style={[
                      styles.iconText,
                      selectedIcon === icon.icon && { color: '#FFFFFF' }
                    ]}
                  >
                    {icon.name}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Schedule */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.sectionTitle}>Schedule</ThemedText>
            <View style={styles.scheduleContainer}>
              {/* Frequency */}
              <TouchableOpacity
                style={styles.scheduleItem}
                onPress={() => {
                  // In a real app, this would open a modal or navigate to a frequency selection screen
                  const frequencies: HabitFrequency[] = ['daily', 'weekly', 'monthly', 'custom'];
                  const currentIndex = frequencies.indexOf(frequency);
                  const nextIndex = (currentIndex + 1) % frequencies.length;
                  setFrequency(frequencies[nextIndex]);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <MaterialIcons name="event_repeat" size={24} color="#0EA5E9" />
                <View style={styles.scheduleTextContainer}>
                  <ThemedText style={styles.scheduleLabel}>Frequency</ThemedText>
                  <ThemedText style={styles.scheduleValue}>
                    {frequency === 'daily' ? 'Everyday' :
                     frequency === 'weekly' ? 'Weekly' :
                     frequency === 'monthly' ? 'Monthly' : 'Custom'}
                  </ThemedText>
                </View>
                <MaterialIcons name="chevron_right" size={24} color="#94A3B8" />
              </TouchableOpacity>

              {/* Time of Day */}
              <TouchableOpacity
                style={styles.scheduleItem}
                onPress={() => {
                  // In a real app, this would open a time picker
                  setTimeOfDay(timeOfDay === 'anytime' ? 'morning' :
                              timeOfDay === 'morning' ? 'afternoon' :
                              timeOfDay === 'afternoon' ? 'evening' : 'anytime');
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <MaterialIcons name="schedule" size={24} color="#0EA5E9" />
                <View style={styles.scheduleTextContainer}>
                  <ThemedText style={styles.scheduleLabel}>Time of day</ThemedText>
                  <ThemedText style={styles.scheduleValue}>
                    {timeOfDay === 'anytime' ? 'Anytime' :
                     timeOfDay === 'morning' ? 'Morning' :
                     timeOfDay === 'afternoon' ? 'Afternoon' :
                     timeOfDay === 'evening' ? 'Evening' : 'Specific time'}
                  </ThemedText>
                </View>
                <MaterialIcons name="chevron_right" size={24} color="#94A3B8" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Reminders */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.sectionTitle}>Reminders</ThemedText>
            <View style={styles.reminderContainer}>
              <View style={styles.reminderItem}>
                <MaterialIcons name="notifications_active" size={24} color="#0EA5E9" />
                <View style={styles.scheduleTextContainer}>
                  <ThemedText style={styles.scheduleLabel}>Enable Reminders</ThemedText>
                  <ThemedText style={styles.reminderStatus}>
                    {reminderEnabled ? 'On' : 'Off'}
                  </ThemedText>
                </View>
                <Switch
                  value={reminderEnabled}
                  onValueChange={(value) => {
                    setReminderEnabled(value);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  trackColor={{ false: '#D1D5DB', true: '#0EA5E9' }}
                  thumbColor="#FFFFFF"
                  ios_backgroundColor="#D1D5DB"
                  style={styles.reminderSwitch}
                />
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Create Button */}
        <SafeAreaView style={styles.footer}>
          <TouchableOpacity
            style={styles.createButton}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <MaterialIcons name="add_circle" size={24} color="#FFFFFF" style={styles.createButtonIcon} />
            <ThemedText style={styles.createButtonText}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Update Habit' : 'Create Habit'}
            </ThemedText>
          </TouchableOpacity>
        </SafeAreaView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F9FF', // Light blue background
  },
  mainContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100, // Extra padding at bottom for the fixed button
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  optionalText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#64748B',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    height: 56,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
    height: '100%',
  },
  textArea: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    padding: 16,
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
    minHeight: 128,
    textAlignVertical: 'top',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  errorText: {
    color: '#EF4444',
    marginTop: 4,
    fontSize: 12,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  iconItem: {
    width: '18%', // Approximately 5 icons per row with gap
    aspectRatio: 1,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  iconText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  scheduleContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  scheduleTextContainer: {
    flex: 1,
    marginLeft: 16,
  },
  scheduleLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  scheduleValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0EA5E9',
  },
  reminderContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reminderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  reminderStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  reminderSwitch: {
    transform: [{ scale: 0.8 }],
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: Platform.OS === 'ios' ? 32 : 16, // Account for bottom safe area
    backdropFilter: 'blur(8px)',
  },
  createButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0EA5E9',
    borderRadius: 12,
    height: 56,
    shadowColor: '#0EA5E9',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  createButtonIcon: {
    marginRight: 8,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '700',
  },
});
