import React, { useState } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Switch,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { format } from 'date-fns';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Habit, HabitCategory, HabitFrequency, HabitWeekdays } from '@/types/habit';
import { HABIT_CATEGORIES } from '@/constants/HabitCategories';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

interface HabitFormProps {
  initialValues?: Partial<Habit>;
  onSubmit: (habit: Omit<Habit, 'id' | 'createdAt' | 'completions'>) => Promise<void>;
  isEditing?: boolean;
}

export function HabitForm({ initialValues, onSubmit, isEditing = false }: HabitFormProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  // Form state
  const [name, setName] = useState(initialValues?.name || '');
  const [description, setDescription] = useState(initialValues?.description || '');
  const [category, setCategory] = useState<HabitCategory>(initialValues?.category || 'productivity');
  const [frequency, setFrequency] = useState<HabitFrequency>(initialValues?.frequency || 'daily');
  const [weekdays, setWeekdays] = useState<HabitWeekdays>(initialValues?.weekdays || {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: true,
    sunday: true,
  });
  const [targetDays, setTargetDays] = useState<number>(initialValues?.targetDays || 1);
  const [reminderEnabled, setReminderEnabled] = useState(!!initialValues?.reminderTime);
  const [reminderTime, setReminderTime] = useState(initialValues?.reminderTime || '20:00');
  const [startDate, setStartDate] = useState(initialValues?.startDate || new Date().toISOString());
  const [endDate, setEndDate] = useState(initialValues?.endDate || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Validation
  const [nameError, setNameError] = useState('');
  
  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!name.trim()) {
      setNameError('Habit name is required');
      return;
    }
    
    setIsSubmitting(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    try {
      await onSubmit({
        name,
        description,
        category,
        frequency,
        weekdays: frequency === 'custom' ? weekdays : undefined,
        targetDays: frequency === 'custom' ? targetDays : undefined,
        startDate,
        endDate: endDate || undefined,
        reminderTime: reminderEnabled ? reminderTime : undefined,
        archived: initialValues?.archived || false,
      });
      
      router.back();
    } catch (error) {
      console.error('Error submitting habit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Toggle a weekday
  const toggleWeekday = (day: keyof HabitWeekdays) => {
    setWeekdays(prev => ({
      ...prev,
      [day]: !prev[day],
    }));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Name Input */}
        <ThemedView style={styles.formGroup}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Habit Name*
          </ThemedText>
          <TextInput
            style={[
              styles.input,
              { color: colors.text, borderColor: nameError ? '#FF5252' : colors.tabIconDefault },
            ]}
            placeholder="Enter habit name"
            placeholderTextColor={colors.tabIconDefault}
            value={name}
            onChangeText={(text) => {
              setName(text);
              if (text.trim()) setNameError('');
            }}
          />
          {nameError ? (
            <ThemedText style={styles.errorText}>{nameError}</ThemedText>
          ) : null}
        </ThemedView>
        
        {/* Description Input */}
        <ThemedView style={styles.formGroup}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Description (Optional)
          </ThemedText>
          <TextInput
            style={[
              styles.input,
              styles.textArea,
              { color: colors.text, borderColor: colors.tabIconDefault },
            ]}
            placeholder="Enter description"
            placeholderTextColor={colors.tabIconDefault}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
          />
        </ThemedView>
        
        {/* Category Selection */}
        <ThemedView style={styles.formGroup}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Category
          </ThemedText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryContainer}
          >
            {HABIT_CATEGORIES.map((cat) => (
              <TouchableOpacity
                key={cat.id}
                style={[
                  styles.categoryItem,
                  category === cat.id && { backgroundColor: cat.color + '20' },
                  { borderColor: cat.color },
                ]}
                onPress={() => {
                  setCategory(cat.id);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <MaterialIcons
                  name={cat.icon}
                  size={24}
                  color={cat.color}
                  style={styles.categoryIcon}
                />
                <ThemedText style={styles.categoryText}>{cat.name}</ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </ThemedView>
        
        {/* Frequency Selection */}
        <ThemedView style={styles.formGroup}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Frequency
          </ThemedText>
          <View style={styles.frequencyContainer}>
            {(['daily', 'weekly', 'monthly', 'custom'] as HabitFrequency[]).map((freq) => (
              <TouchableOpacity
                key={freq}
                style={[
                  styles.frequencyItem,
                  frequency === freq && { backgroundColor: colors.tint, borderColor: colors.tint },
                ]}
                onPress={() => {
                  setFrequency(freq);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <ThemedText
                  style={[
                    styles.frequencyText,
                    frequency === freq && { color: '#FFFFFF' },
                  ]}
                >
                  {freq.charAt(0).toUpperCase() + freq.slice(1)}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </ThemedView>
        
        {/* Custom Frequency Options */}
        {frequency === 'custom' && (
          <ThemedView style={styles.formGroup}>
            <ThemedText type="defaultSemiBold" style={styles.label}>
              Custom Schedule
            </ThemedText>
            
            <View style={styles.weekdaysContainer}>
              {(Object.keys(weekdays) as Array<keyof HabitWeekdays>).map((day) => (
                <TouchableOpacity
                  key={day}
                  style={[
                    styles.weekdayItem,
                    weekdays[day] && { backgroundColor: colors.tint, borderColor: colors.tint },
                  ]}
                  onPress={() => toggleWeekday(day)}
                >
                  <ThemedText
                    style={[
                      styles.weekdayText,
                      weekdays[day] && { color: '#FFFFFF' },
                    ]}
                  >
                    {day.charAt(0).toUpperCase()}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.targetDaysContainer}>
              <ThemedText style={styles.targetDaysLabel}>
                Target days per week:
              </ThemedText>
              <View style={styles.targetDaysControls}>
                <TouchableOpacity
                  style={styles.targetDaysButton}
                  onPress={() => {
                    if (targetDays > 1) {
                      setTargetDays(targetDays - 1);
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    }
                  }}
                >
                  <MaterialIcons name="remove" size={20} color={colors.tabIconDefault} />
                </TouchableOpacity>
                
                <ThemedText style={styles.targetDaysValue}>{targetDays}</ThemedText>
                
                <TouchableOpacity
                  style={styles.targetDaysButton}
                  onPress={() => {
                    if (targetDays < 7) {
                      setTargetDays(targetDays + 1);
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    }
                  }}
                >
                  <MaterialIcons name="add" size={20} color={colors.tabIconDefault} />
                </TouchableOpacity>
              </View>
            </View>
          </ThemedView>
        )}
        
        {/* Reminder Toggle */}
        <ThemedView style={styles.formGroup}>
          <View style={styles.reminderHeader}>
            <ThemedText type="defaultSemiBold" style={styles.label}>
              Daily Reminder
            </ThemedText>
            <Switch
              value={reminderEnabled}
              onValueChange={(value) => {
                setReminderEnabled(value);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              trackColor={{ false: '#767577', true: colors.tint }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          {reminderEnabled && (
            <View style={styles.reminderTimeContainer}>
              <ThemedText style={styles.reminderTimeLabel}>
                Reminder time:
              </ThemedText>
              <TextInput
                style={[
                  styles.reminderTimeInput,
                  { color: colors.text, borderColor: colors.tabIconDefault },
                ]}
                placeholder="20:00"
                placeholderTextColor={colors.tabIconDefault}
                value={reminderTime}
                onChangeText={setReminderTime}
                keyboardType="numbers-and-punctuation"
              />
            </View>
          )}
        </ThemedView>
        
        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: colors.tint }]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <ThemedText style={styles.submitButtonText}>
            {isSubmitting ? 'Saving...' : isEditing ? 'Update Habit' : 'Create Habit'}
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#FF5252',
    marginTop: 4,
    fontSize: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 12,
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    fontSize: 14,
  },
  frequencyContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  frequencyItem: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    marginBottom: 8,
    borderColor: '#CCCCCC',
  },
  frequencyText: {
    fontSize: 14,
  },
  weekdaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  weekdayItem: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#CCCCCC',
  },
  weekdayText: {
    fontSize: 14,
    fontWeight: '500',
  },
  targetDaysContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  targetDaysLabel: {
    fontSize: 14,
  },
  targetDaysControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  targetDaysButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#CCCCCC',
  },
  targetDaysValue: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 12,
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reminderTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  reminderTimeLabel: {
    fontSize: 14,
    marginRight: 12,
  },
  reminderTimeInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    width: 80,
  },
  submitButton: {
    borderRadius: 24,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
