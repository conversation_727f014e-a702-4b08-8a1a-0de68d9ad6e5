import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { format, parseISO, eachDayOfInterval, subDays, isSameDay } from 'date-fns';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Habit, HabitCompletion } from '@/types/habit';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { getCategoryInfo } from '@/constants/HabitCategories';

interface StreakCalendarProps {
  habit: Habit;
  days?: number; // Number of days to show
}

export function StreakCalendar({ habit, days = 7 }: StreakCalendarProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  const categoryInfo = getCategoryInfo(habit.category);
  
  // Generate dates for the last N days
  const today = new Date();
  const startDate = subDays(today, days - 1);
  const dateRange = eachDayOfInterval({ start: startDate, end: today });
  
  // Check if a date has a completion
  const getCompletionForDate = (date: Date): HabitCompletion | undefined => {
    return habit.completions.find(completion => 
      isSameDay(parseISO(completion.date), date)
    );
  };
  
  return (
    <ThemedView style={styles.container}>
      <ThemedText type="defaultSemiBold" style={styles.title}>
        Last {days} Days
      </ThemedText>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.calendarContainer}
      >
        {dateRange.map((date, index) => {
          const completion = getCompletionForDate(date);
          const isCompleted = completion?.completed || false;
          
          return (
            <View key={index} style={styles.dayContainer}>
              <ThemedText style={styles.dayText}>
                {format(date, 'EEE')}
              </ThemedText>
              
              <View 
                style={[
                  styles.dateCircle,
                  isCompleted 
                    ? { backgroundColor: categoryInfo.color } 
                    : { borderColor: colors.tabIconDefault, borderWidth: 1 }
                ]}
              >
                <ThemedText 
                  style={[
                    styles.dateText,
                    isCompleted && { color: '#FFFFFF' }
                  ]}
                >
                  {format(date, 'd')}
                </ThemedText>
              </View>
            </View>
          );
        })}
      </ScrollView>
      
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <ThemedText type="defaultSemiBold" style={styles.statValue}>
            {habit.completions.filter(c => c.completed).length}
          </ThemedText>
          <ThemedText style={styles.statLabel}>Total</ThemedText>
        </View>
        
        <View style={styles.statDivider} />
        
        <View style={styles.statItem}>
          <ThemedText 
            type="defaultSemiBold" 
            style={[styles.statValue, { color: categoryInfo.color }]}
          >
            {/* This is a simplified streak calculation */}
            {calculateCurrentStreak(habit)}
          </ThemedText>
          <ThemedText style={styles.statLabel}>Current Streak</ThemedText>
        </View>
        
        <View style={styles.statDivider} />
        
        <View style={styles.statItem}>
          <ThemedText type="defaultSemiBold" style={styles.statValue}>
            {/* This is a simplified best streak calculation */}
            {calculateBestStreak(habit)}
          </ThemedText>
          <ThemedText style={styles.statLabel}>Best Streak</ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

// Helper function to calculate current streak (simplified)
function calculateCurrentStreak(habit: Habit): number {
  let streak = 0;
  let currentDate = new Date();
  let streakBroken = false;
  
  while (!streakBroken) {
    const completion = habit.completions.find(c => 
      isSameDay(parseISO(c.date), currentDate)
    );
    
    if (completion?.completed) {
      streak++;
      currentDate = subDays(currentDate, 1);
    } else {
      // If it's today and not completed, don't break the streak yet
      if (isSameDay(currentDate, new Date()) && streak > 0) {
        currentDate = subDays(currentDate, 1);
      } else {
        streakBroken = true;
      }
    }
  }
  
  return streak;
}

// Helper function to calculate best streak (simplified)
function calculateBestStreak(habit: Habit): number {
  // For now, just return the current streak
  // A more sophisticated implementation would track all streaks
  return calculateCurrentStreak(habit);
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  title: {
    marginBottom: 12,
  },
  calendarContainer: {
    paddingVertical: 8,
  },
  dayContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  dayText: {
    fontSize: 12,
    marginBottom: 4,
  },
  dateCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#CCCCCC',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    backgroundColor: '#CCCCCC',
  },
});
