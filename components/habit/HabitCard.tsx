import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Habit } from '@/types/habit';
import { getCategoryInfo } from '@/constants/HabitCategories';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { format, isToday, parseISO } from 'date-fns';

interface HabitCardProps {
  habit: Habit;
  onToggleCompletion: (id: string) => void;
}

export function HabitCard({ habit, onToggleCompletion }: HabitCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  const categoryInfo = getCategoryInfo(habit.category);
  
  // Check if habit is completed today
  const isCompletedToday = React.useMemo(() => {
    return habit.completions.some(
      completion => isToday(parseISO(completion.date)) && completion.completed
    );
  }, [habit.completions]);
  
  // Calculate current streak
  const currentStreak = React.useMemo(() => {
    // This is a simplified version - the full logic is in useHabits.ts
    return habit.completions.filter(c => c.completed).length;
  }, [habit.completions]);
  
  const handlePress = () => {
    router.push(`/habit/${habit.id}`);
  };
  
  const handleToggleCompletion = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onToggleCompletion(habit.id);
  };
  
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <ThemedView 
        style={[
          styles.card, 
          { borderLeftColor: categoryInfo.color, borderLeftWidth: 4 }
        ]}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <MaterialIcons 
                name={categoryInfo.icon} 
                size={24} 
                color={categoryInfo.color} 
                style={styles.icon} 
              />
              <ThemedText type="defaultSemiBold" style={styles.title}>
                {habit.name}
              </ThemedText>
            </View>
            
            <TouchableOpacity 
              style={[
                styles.checkButton, 
                isCompletedToday ? styles.checkButtonCompleted : null,
                { borderColor: isCompletedToday ? categoryInfo.color : colors.tabIconDefault }
              ]} 
              onPress={handleToggleCompletion}
            >
              {isCompletedToday ? (
                <MaterialIcons 
                  name="check" 
                  size={20} 
                  color={categoryInfo.color} 
                />
              ) : null}
            </TouchableOpacity>
          </View>
          
          {habit.description ? (
            <ThemedText style={styles.description} numberOfLines={2}>
              {habit.description}
            </ThemedText>
          ) : null}
          
          <View style={styles.footer}>
            <View style={styles.streakContainer}>
              <MaterialIcons 
                name="local-fire-department" 
                size={16} 
                color={colors.tabIconDefault} 
              />
              <ThemedText style={styles.streakText}>
                {currentStreak} day{currentStreak !== 1 ? 's' : ''}
              </ThemedText>
            </View>
            
            {habit.reminderTime ? (
              <View style={styles.reminderContainer}>
                <MaterialIcons 
                  name="notifications" 
                  size={16} 
                  color={colors.tabIconDefault} 
                />
                <ThemedText style={styles.reminderText}>
                  {habit.reminderTime}
                </ThemedText>
              </View>
            ) : null}
          </View>
        </View>
      </ThemedView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  card: {
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    flex: 1,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  checkButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkButtonCompleted: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  streakText: {
    fontSize: 12,
    marginLeft: 4,
  },
  reminderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reminderText: {
    fontSize: 12,
    marginLeft: 4,
  },
});
