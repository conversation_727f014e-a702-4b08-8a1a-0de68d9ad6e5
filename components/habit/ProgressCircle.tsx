import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import Animated, { useAnimatedProps, useSharedValue, withTiming } from 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface ProgressCircleProps {
  progress: number; // 0 to 1
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showPercentage?: boolean;
  duration?: number;
}

export function ProgressCircle({
  progress,
  size = 100,
  strokeWidth = 10,
  color,
  backgroundColor,
  showPercentage = true,
  duration = 1000,
}: ProgressCircleProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const progressColor = color || colors.tint;
  const bgColor = backgroundColor || (colorScheme === 'dark' ? '#333' : '#E0E0E0');
  
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;
  
  const progressValue = useSharedValue(0);
  
  React.useEffect(() => {
    progressValue.value = withTiming(progress, { duration });
  }, [progress, duration, progressValue]);
  
  const animatedProps = useAnimatedProps(() => {
    const strokeDashoffset = circumference * (1 - progressValue.value);
    return { strokeDashoffset };
  });
  
  const percentage = Math.round(progress * 100);
  
  return (
    <View style={styles.container}>
      <Svg width={size} height={size}>
        {/* Background Circle */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          stroke={bgColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress Circle */}
        <AnimatedCircle
          cx={center}
          cy={center}
          r={radius}
          stroke={progressColor}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          fill="transparent"
          strokeDasharray={circumference}
          animatedProps={animatedProps}
          // Start from the top (12 o'clock position)
          transform={`rotate(-90, ${center}, ${center})`}
        />
        
        {/* Percentage Text */}
        {showPercentage && (
          <SvgText
            x={center}
            y={center + 5} // Slight adjustment for visual centering
            fontSize={size / 5}
            fontWeight="bold"
            fill={colorScheme === 'dark' ? '#FFFFFF' : '#000000'}
            textAnchor="middle"
            alignmentBaseline="middle"
          >
            {percentage}%
          </SvgText>
        )}
      </Svg>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
