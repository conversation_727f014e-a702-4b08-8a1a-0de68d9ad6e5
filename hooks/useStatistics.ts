import { useMemo } from 'react';
import { format, parseISO, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay } from 'date-fns';
import { Habit, HabitStats } from '@/types/habit';
import { useHabits } from './useHabits';

export function useStatistics() {
  const { habits, calculateHabitStats } = useHabits();
  
  // Calculate stats for all habits
  const allStats = useMemo(() => {
    return habits.map(calculateHabitStats);
  }, [habits, calculateHabitStats]);
  
  // Get overall completion rate
  const overallCompletionRate = useMemo(() => {
    if (allStats.length === 0) return 0;
    
    const totalCompletions = allStats.reduce((sum, stat) => sum + stat.totalCompletions, 0);
    const totalDays = allStats.reduce((sum, stat) => sum + stat.totalDays, 0);
    
    return totalDays > 0 ? totalCompletions / totalDays : 0;
  }, [allStats]);
  
  // Get total completions
  const totalCompletions = useMemo(() => {
    return allStats.reduce((sum, stat) => sum + stat.totalCompletions, 0);
  }, [allStats]);
  
  // Get longest streak across all habits
  const longestStreak = useMemo(() => {
    if (allStats.length === 0) return 0;
    return Math.max(...allStats.map(stat => stat.longestStreak));
  }, [allStats]);
  
  // Get current streak across all habits
  const currentStreak = useMemo(() => {
    if (allStats.length === 0) return 0;
    return Math.max(...allStats.map(stat => stat.currentStreak));
  }, [allStats]);
  
  // Get weekly data for chart
  const getWeeklyData = (habit?: Habit) => {
    const today = new Date();
    const start = startOfWeek(today, { weekStartsOn: 1 }); // Start on Monday
    const end = endOfWeek(today, { weekStartsOn: 1 });
    
    const days = eachDayOfInterval({ start, end });
    
    if (habit) {
      // Data for a specific habit
      return days.map(day => {
        const dayStr = format(day, 'yyyy-MM-dd');
        const completion = habit.completions.find(c => 
          format(parseISO(c.date), 'yyyy-MM-dd') === dayStr
        );
        
        return {
          day: format(day, 'EEE'),
          completed: completion?.completed ? 1 : 0,
        };
      });
    } else {
      // Data for all habits
      return days.map(day => {
        let completedCount = 0;
        let totalCount = 0;
        
        habits.forEach(habit => {
          // Only count active habits
          if (!habit.archived) {
            totalCount++;
            
            const completed = habit.completions.some(c => 
              isSameDay(parseISO(c.date), day) && c.completed
            );
            
            if (completed) {
              completedCount++;
            }
          }
        });
        
        return {
          day: format(day, 'EEE'),
          completed: totalCount > 0 ? completedCount / totalCount : 0,
        };
      });
    }
  };
  
  // Get monthly completion rate
  const getMonthlyCompletionRate = () => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    let totalCompletions = 0;
    let totalPossibleCompletions = 0;
    
    habits.forEach(habit => {
      if (habit.archived) return;
      
      habit.completions.forEach(completion => {
        const completionDate = parseISO(completion.date);
        if (completionDate.getMonth() === currentMonth && 
            completionDate.getFullYear() === currentYear) {
          if (completion.completed) {
            totalCompletions++;
          }
          totalPossibleCompletions++;
        }
      });
    });
    
    return totalPossibleCompletions > 0 ? totalCompletions / totalPossibleCompletions : 0;
  };
  
  // Get habits sorted by completion rate
  const getHabitsByCompletionRate = () => {
    return [...allStats].sort((a, b) => b.completionRate - a.completionRate);
  };
  
  return {
    allStats,
    overallCompletionRate,
    totalCompletions,
    longestStreak,
    currentStreak,
    getWeeklyData,
    getMonthlyCompletionRate,
    getHabitsByCompletionRate,
  };
}
