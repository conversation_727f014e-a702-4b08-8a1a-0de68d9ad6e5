import { useState, useEffect, useCallback } from 'react';
import { format, isToday, parseISO, startOfDay, subDays } from 'date-fns';
import { Habit, HabitCompletion, HabitStats } from '@/types/habit';
import { HabitStorage } from '@/services/storage';
import { NotificationService } from '@/services/notification';

export function useHabits() {
  const [habits, setHabits] = useState<Habit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load habits from storage
  const loadHabits = useCallback(async () => {
    try {
      setLoading(true);
      const storedHabits = await HabitStorage.getHabits();
      setHabits(storedHabits);
      setError(null);
    } catch (err) {
      setError('Failed to load habits');
      console.error('Error loading habits:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load habits on mount
  useEffect(() => {
    loadHabits();
  }, [loadHabits]);

  // Create a new habit
  const createHabit = async (habit: Omit<Habit, 'id' | 'createdAt' | 'completions'>): Promise<Habit | null> => {
    try {
      const newHabit: Habit = {
        ...habit,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        completions: [],
        archived: false,
      };

      const success = await HabitStorage.saveHabit(newHabit);
      
      if (success) {
        setHabits(prevHabits => [...prevHabits, newHabit]);
        
        // Schedule reminder if set
        if (newHabit.reminderTime) {
          await NotificationService.scheduleHabitReminder(newHabit);
        }
        
        return newHabit;
      }
      
      return null;
    } catch (err) {
      setError('Failed to create habit');
      console.error('Error creating habit:', err);
      return null;
    }
  };

  // Update an existing habit
  const updateHabit = async (updatedHabit: Habit): Promise<boolean> => {
    try {
      const success = await HabitStorage.updateHabit(updatedHabit);
      
      if (success) {
        setHabits(prevHabits => 
          prevHabits.map(habit => 
            habit.id === updatedHabit.id ? updatedHabit : habit
          )
        );
        
        // Update reminder if needed
        if (updatedHabit.reminderTime) {
          await NotificationService.scheduleHabitReminder(updatedHabit);
        } else {
          await NotificationService.cancelHabitReminder(updatedHabit.id);
        }
        
        return true;
      }
      
      return false;
    } catch (err) {
      setError('Failed to update habit');
      console.error('Error updating habit:', err);
      return false;
    }
  };

  // Delete a habit
  const deleteHabit = async (id: string): Promise<boolean> => {
    try {
      const success = await HabitStorage.deleteHabit(id);
      
      if (success) {
        setHabits(prevHabits => prevHabits.filter(habit => habit.id !== id));
        
        // Cancel any reminders
        await NotificationService.cancelHabitReminder(id);
        
        return true;
      }
      
      return false;
    } catch (err) {
      setError('Failed to delete habit');
      console.error('Error deleting habit:', err);
      return false;
    }
  };

  // Archive a habit
  const archiveHabit = async (id: string): Promise<boolean> => {
    try {
      const success = await HabitStorage.archiveHabit(id);
      
      if (success) {
        setHabits(prevHabits => 
          prevHabits.map(habit => 
            habit.id === id ? { ...habit, archived: true } : habit
          )
        );
        
        // Cancel any reminders
        await NotificationService.cancelHabitReminder(id);
        
        return true;
      }
      
      return false;
    } catch (err) {
      setError('Failed to archive habit');
      console.error('Error archiving habit:', err);
      return false;
    }
  };

  // Toggle habit completion for today
  const toggleHabitCompletion = async (habitId: string, notes?: string): Promise<boolean> => {
    try {
      const habit = habits.find(h => h.id === habitId);
      if (!habit) return false;
      
      const today = new Date().toISOString();
      const todayFormatted = format(new Date(), 'yyyy-MM-dd');
      
      // Check if there's already a completion for today
      const existingCompletionIndex = habit.completions.findIndex(
        completion => format(parseISO(completion.date), 'yyyy-MM-dd') === todayFormatted
      );
      
      let updatedCompletions: HabitCompletion[];
      
      if (existingCompletionIndex >= 0) {
        // Toggle existing completion
        updatedCompletions = [...habit.completions];
        updatedCompletions[existingCompletionIndex] = {
          ...updatedCompletions[existingCompletionIndex],
          completed: !updatedCompletions[existingCompletionIndex].completed,
          notes: notes || updatedCompletions[existingCompletionIndex].notes,
        };
      } else {
        // Add new completion for today
        updatedCompletions = [
          ...habit.completions,
          { date: today, completed: true, notes }
        ];
      }
      
      const updatedHabit: Habit = {
        ...habit,
        completions: updatedCompletions,
      };
      
      return await updateHabit(updatedHabit);
    } catch (err) {
      setError('Failed to toggle habit completion');
      console.error('Error toggling habit completion:', err);
      return false;
    }
  };

  // Get active (non-archived) habits
  const getActiveHabits = useCallback(() => {
    return habits.filter(habit => !habit.archived);
  }, [habits]);

  // Get archived habits
  const getArchivedHabits = useCallback(() => {
    return habits.filter(habit => habit.archived);
  }, [habits]);

  // Get habits due today
  const getTodayHabits = useCallback(() => {
    const activeHabits = getActiveHabits();
    // For now, return all active habits - we'll implement frequency filtering later
    return activeHabits;
  }, [getActiveHabits]);

  // Calculate statistics for a habit
  const calculateHabitStats = useCallback((habit: Habit): HabitStats => {
    const completions = habit.completions.filter(c => c.completed);
    const totalCompletions = completions.length;
    
    // Calculate days since habit creation
    const creationDate = parseISO(habit.startDate);
    const today = startOfDay(new Date());
    const daysSinceCreation = Math.max(1, Math.floor((today.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
    
    // Find the last completed date
    let lastCompleted: string | undefined;
    if (completions.length > 0) {
      lastCompleted = completions
        .sort((a, b) => parseISO(b.date).getTime() - parseISO(a.date).getTime())[0]
        .date;
    }
    
    // Calculate current streak
    let currentStreak = 0;
    let checkDate = today;
    let streakBroken = false;
    
    while (!streakBroken) {
      const dateStr = format(checkDate, 'yyyy-MM-dd');
      const completed = habit.completions.some(
        c => format(parseISO(c.date), 'yyyy-MM-dd') === dateStr && c.completed
      );
      
      if (completed) {
        currentStreak++;
        checkDate = subDays(checkDate, 1);
      } else {
        // If it's today and not completed, don't break the streak yet
        if (isToday(checkDate) && currentStreak > 0) {
          checkDate = subDays(checkDate, 1);
        } else {
          streakBroken = true;
        }
      }
    }
    
    // Calculate longest streak (simplified version)
    // A more accurate implementation would account for habit frequency
    const longestStreak = currentStreak; // For now, just use current streak
    
    return {
      id: habit.id,
      name: habit.name,
      totalCompletions,
      totalDays: daysSinceCreation,
      completionRate: totalCompletions / daysSinceCreation,
      currentStreak,
      longestStreak,
      lastCompleted,
    };
  }, []);

  return {
    habits,
    loading,
    error,
    loadHabits,
    createHabit,
    updateHabit,
    deleteHabit,
    archiveHabit,
    toggleHabitCompletion,
    getActiveHabits,
    getArchivedHabits,
    getTodayHabits,
    calculateHabitStats,
  };
}
