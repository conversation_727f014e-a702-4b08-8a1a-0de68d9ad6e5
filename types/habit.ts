export type HabitFrequency = 'daily' | 'weekly' | 'monthly' | 'custom';

export type HabitCategory =
  | 'health'
  | 'fitness'
  | 'productivity'
  | 'mindfulness'
  | 'learning'
  | 'finance'
  | 'social'
  | 'other';

export interface HabitWeekdays {
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturday: boolean;
  sunday: boolean;
}

export interface HabitCompletion {
  date: string; // ISO string
  completed: boolean;
  notes?: string;
}

export interface Habit {
  id: string;
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  weekdays?: HabitWeekdays; // For custom frequency
  targetDays?: number; // For custom frequency (e.g., 3 times per week)
  createdAt: string; // ISO string
  startDate: string; // ISO string
  endDate?: string; // ISO string (optional)
  reminderTime?: string; // HH:MM format
  color?: string;
  icon?: string; // MaterialIcons name
  timeOfDay?: string; // 'anytime', 'morning', 'afternoon', 'evening', 'specific'
  completions: HabitCompletion[];
  archived: boolean;
}

export interface HabitStats {
  id: string;
  name: string;
  totalCompletions: number;
  totalDays: number;
  completionRate: number;
  currentStreak: number;
  longestStreak: number;
  lastCompleted?: string; // ISO string
}
