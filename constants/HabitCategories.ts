import { HabitCategory } from '@/types/habit';
import { MaterialIcons } from '@expo/vector-icons';

export interface CategoryInfo {
  id: HabitCategory;
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  color: string;
  lightColor: string;
  darkColor: string;
}

export const HABIT_CATEGORIES: CategoryInfo[] = [
  {
    id: 'health',
    name: 'Health',
    icon: 'favorite',
    color: '#FF5252',
    lightColor: '#FFCDD2',
    darkColor: '#B71C1C',
  },
  {
    id: 'fitness',
    name: 'Fitness',
    icon: 'fitness-center',
    color: '#FF9800',
    lightColor: '#FFE0B2',
    darkColor: '#E65100',
  },
  {
    id: 'productivity',
    name: 'Productivity',
    icon: 'check-circle',
    color: '#4CAF50',
    lightColor: '#C8E6C9',
    darkColor: '#1B5E20',
  },
  {
    id: 'mindfulness',
    name: 'Mindfulness',
    icon: 'self-improvement',
    color: '#9C27B0',
    lightColor: '#E1BEE7',
    darkColor: '#4A148C',
  },
  {
    id: 'learning',
    name: 'Learning',
    icon: 'school',
    color: '#2196F3',
    lightColor: '#BBDEFB',
    darkColor: '#0D47A1',
  },
  {
    id: 'finance',
    name: 'Finance',
    icon: 'account-balance',
    color: '#607D8B',
    lightColor: '#CFD8DC',
    darkColor: '#263238',
  },
  {
    id: 'social',
    name: 'Social',
    icon: 'people',
    color: '#E91E63',
    lightColor: '#F8BBD0',
    darkColor: '#880E4F',
  },
  {
    id: 'other',
    name: 'Other',
    icon: 'more-horiz',
    color: '#9E9E9E',
    lightColor: '#EEEEEE',
    darkColor: '#424242',
  },
];

export function getCategoryInfo(categoryId: HabitCategory): CategoryInfo {
  return HABIT_CATEGORIES.find(category => category.id === categoryId) || HABIT_CATEGORIES[7]; // Default to 'other'
}
