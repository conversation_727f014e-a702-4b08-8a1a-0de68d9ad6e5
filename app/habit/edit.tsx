import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { HabitForm } from '@/components/habit/HabitForm';
import { useHabits } from '@/hooks/useHabits';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Habit } from '@/types/habit';

export default function EditHabitScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const { habits, loadHabits, updateHabit } = useHabits();
  
  const [habit, setHabit] = useState<Habit | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Load habit data
  useEffect(() => {
    const loadHabitData = async () => {
      try {
        setLoading(true);
        await loadHabits();
        
        if (id) {
          const foundHabit = habits.find(h => h.id === id);
          setHabit(foundHabit || null);
        }
      } catch (error) {
        console.error('Error loading habit:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadHabitData();
  }, [id, loadHabits, habits]);
  
  const handleSubmit = async (habitData: Omit<Habit, 'id' | 'createdAt' | 'completions'>) => {
    if (!habit) return;
    
    try {
      const updatedHabit: Habit = {
        ...habit,
        ...habitData,
      };
      
      const success = await updateHabit(updatedHabit);
      
      if (success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        router.back();
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    } catch (error) {
      console.error('Error updating habit:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };
  
  const handleClose = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.back();
  };
  
  if (loading || !habit) {
    return (
      <ThemedView style={[styles.container, styles.loadingContainer]}>
        <ThemedText>Loading habit...</ThemedText>
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Edit Habit',
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity onPress={handleClose} style={styles.headerButton}>
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <HabitForm 
        initialValues={habit} 
        onSubmit={handleSubmit} 
        isEditing={true} 
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
  },
});
