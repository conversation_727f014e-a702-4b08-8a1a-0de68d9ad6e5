import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { format } from 'date-fns';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { StreakCalendar } from '@/components/habit/StreakCalendar';
import { StatisticsChart } from '@/components/statistics/StatisticsChart';
import { useHabits } from '@/hooks/useHabits';
import { useStatistics } from '@/hooks/useStatistics';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Habit } from '@/types/habit';
import { getCategoryInfo } from '@/constants/HabitCategories';

export default function HabitDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const { habits, loadHabits, updateHabit, deleteHabit, archiveHabit, calculateHabitStats } = useHabits();
  const { getWeeklyData } = useStatistics();
  
  const [habit, setHabit] = useState<Habit | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Load habit data
  useEffect(() => {
    const loadHabitData = async () => {
      try {
        setLoading(true);
        await loadHabits();
        
        if (id) {
          const foundHabit = habits.find(h => h.id === id);
          setHabit(foundHabit || null);
        }
      } catch (error) {
        console.error('Error loading habit:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadHabitData();
  }, [id, loadHabits, habits]);
  
  // Calculate stats for this habit
  const stats = habit ? calculateHabitStats(habit) : null;
  
  // Get weekly data for this habit
  const weeklyData = habit ? getWeeklyData(habit) : [];
  
  // Get category info
  const categoryInfo = habit ? getCategoryInfo(habit.category) : null;
  
  // Handle editing the habit
  const handleEdit = () => {
    if (habit) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      router.push({
        pathname: '/habit/edit',
        params: { id: habit.id }
      });
    }
  };
  
  // Handle deleting the habit
  const handleDelete = () => {
    if (!habit) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    Alert.alert(
      'Delete Habit',
      `Are you sure you want to delete "${habit.name}"? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteHabit(habit.id);
              
              if (success) {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                router.back();
              } else {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              }
            } catch (error) {
              console.error('Error deleting habit:', error);
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
          },
        },
      ]
    );
  };
  
  // Handle archiving the habit
  const handleArchive = () => {
    if (!habit) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    Alert.alert(
      'Archive Habit',
      `Are you sure you want to archive "${habit.name}"? It will be moved to your archived habits.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Archive',
          onPress: async () => {
            try {
              const success = await archiveHabit(habit.id);
              
              if (success) {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                router.back();
              } else {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              }
            } catch (error) {
              console.error('Error archiving habit:', error);
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
          },
        },
      ]
    );
  };
  
  // Handle going back
  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };
  
  if (loading || !habit || !categoryInfo) {
    return (
      <ThemedView style={[styles.container, styles.loadingContainer]}>
        <ThemedText>Loading habit...</ThemedText>
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      <Stack.Screen
        options={{
          title: habit.name,
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.headerButton}>
              <MaterialIcons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerRightContainer}>
              <TouchableOpacity onPress={handleEdit} style={styles.headerButton}>
                <MaterialIcons name="edit" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDelete} style={styles.headerButton}>
                <MaterialIcons name="delete" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Habit Header */}
        <View style={styles.habitHeader}>
          <View style={[styles.categoryBadge, { backgroundColor: categoryInfo.color }]}>
            <MaterialIcons name={categoryInfo.icon} size={24} color="#FFFFFF" />
            <ThemedText style={styles.categoryText}>{categoryInfo.name}</ThemedText>
          </View>
          
          {habit.description && (
            <ThemedText style={styles.description}>{habit.description}</ThemedText>
          )}
          
          <View style={styles.metaContainer}>
            <View style={styles.metaItem}>
              <MaterialIcons name="calendar-today" size={16} color={colors.tabIconDefault} />
              <ThemedText style={styles.metaText}>
                Started {format(new Date(habit.startDate), 'MMM d, yyyy')}
              </ThemedText>
            </View>
            
            {habit.reminderTime && (
              <View style={styles.metaItem}>
                <MaterialIcons name="notifications" size={16} color={colors.tabIconDefault} />
                <ThemedText style={styles.metaText}>
                  Reminder at {habit.reminderTime}
                </ThemedText>
              </View>
            )}
            
            <View style={styles.metaItem}>
              <MaterialIcons name="repeat" size={16} color={colors.tabIconDefault} />
              <ThemedText style={styles.metaText}>
                {habit.frequency.charAt(0).toUpperCase() + habit.frequency.slice(1)}
              </ThemedText>
            </View>
          </View>
        </View>
        
        {/* Streak Calendar */}
        <StreakCalendar habit={habit} days={14} />
        
        {/* Weekly Chart */}
        <StatisticsChart
          data={weeklyData}
          title="Weekly Progress"
          description="Your completion rate for each day of the current week."
        />
        
        {/* Stats */}
        {stats && (
          <ThemedView style={styles.statsContainer}>
            <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
              Statistics
            </ThemedText>
            
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <ThemedText style={styles.statValue}>{stats.totalCompletions}</ThemedText>
                <ThemedText style={styles.statLabel}>Total Completions</ThemedText>
              </View>
              
              <View style={styles.statItem}>
                <ThemedText style={styles.statValue}>
                  {Math.round(stats.completionRate * 100)}%
                </ThemedText>
                <ThemedText style={styles.statLabel}>Completion Rate</ThemedText>
              </View>
              
              <View style={styles.statItem}>
                <ThemedText style={styles.statValue}>{stats.currentStreak}</ThemedText>
                <ThemedText style={styles.statLabel}>Current Streak</ThemedText>
              </View>
              
              <View style={styles.statItem}>
                <ThemedText style={styles.statValue}>{stats.longestStreak}</ThemedText>
                <ThemedText style={styles.statLabel}>Longest Streak</ThemedText>
              </View>
            </View>
          </ThemedView>
        )}
        
        {/* Archive Button */}
        <TouchableOpacity
          style={styles.archiveButton}
          onPress={handleArchive}
        >
          <MaterialIcons name="archive" size={20} color={colors.tabIconDefault} />
          <ThemedText style={styles.archiveButtonText}>Archive Habit</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
  },
  headerRightContainer: {
    flexDirection: 'row',
  },
  scrollContent: {
    padding: 16,
  },
  habitHeader: {
    marginBottom: 16,
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
  },
  categoryText: {
    color: '#FFFFFF',
    marginLeft: 4,
    fontWeight: '500',
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
  },
  metaContainer: {
    marginTop: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metaText: {
    fontSize: 14,
    marginLeft: 8,
  },
  statsContainer: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statItem: {
    width: '50%',
    marginBottom: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  archiveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    marginBottom: 24,
  },
  archiveButtonText: {
    marginLeft: 8,
  },
});
