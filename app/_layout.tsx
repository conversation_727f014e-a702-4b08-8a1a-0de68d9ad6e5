import { DarkTheme, De<PERSON>ultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useEffect } from 'react';

import { useColorScheme } from '@/hooks/useColorScheme';
import { NotificationService } from '@/services/notification';
import { SettingsStorage } from '@/services/storage';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Initialize notifications
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Request notification permissions
        await NotificationService.requestPermissions();

        // Set up daily reminder based on settings
        const settings = await SettingsStorage.getSettings();
        if (settings.reminderEnabled && settings.reminderTime) {
          await NotificationService.scheduleDailyReminder(settings.reminderTime);
        }
      } catch (error) {
        console.error('Error initializing notifications:', error);
      }
    };

    initializeNotifications();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
        <Stack.Screen name="habit/[id]" options={{ headerShown: false }} />
        <Stack.Screen name="habit/new" options={{ headerShown: false, presentation: 'modal' }} />
        <Stack.Screen name="habit/edit" options={{ headerShown: false, presentation: 'modal' }} />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}
