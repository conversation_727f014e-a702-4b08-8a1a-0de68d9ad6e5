import React, { useEffect } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { StatisticsChart } from '@/components/statistics/StatisticsChart';
import { ProgressCircle } from '@/components/habit/ProgressCircle';
import { EmptyState } from '@/components/ui/EmptyState';
import { useHabits } from '@/hooks/useHabits';
import { useStatistics } from '@/hooks/useStatistics';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { router } from 'expo-router';

export default function StatisticsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const { habits, loading, error, loadHabits, getActiveHabits } = useHabits();
  const { 
    overallCompletionRate, 
    totalCompletions, 
    longestStreak, 
    currentStreak,
    getWeeklyData,
    getHabitsByCompletionRate
  } = useStatistics();
  
  // Load habits on mount
  useEffect(() => {
    loadHabits();
  }, [loadHabits]);
  
  // Get active habits
  const activeHabits = getActiveHabits();
  
  // Get weekly data for chart
  const weeklyData = getWeeklyData();
  
  // Get top habits by completion rate
  const topHabits = getHabitsByCompletionRate().slice(0, 3);
  
  // Handle creating a new habit
  const handleCreateHabit = () => {
    router.push('/habit/new');
  };
  
  if (activeHabits.length === 0) {
    return (
      <EmptyState
        title="No Statistics Yet"
        message="Create and complete habits to see your statistics."
        icon="bar-chart"
        actionLabel="Create Habit"
        onAction={handleCreateHabit}
      />
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          Statistics
        </ThemedText>
      </View>
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Overall Stats */}
        <ThemedView style={styles.statsContainer}>
          <View style={styles.statRow}>
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>Completion Rate</ThemedText>
              <View style={styles.statValueContainer}>
                <ProgressCircle 
                  progress={overallCompletionRate} 
                  size={80} 
                  showPercentage={true} 
                />
              </View>
            </View>
            
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>Total Completions</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText type="title" style={styles.statValue}>
                  {totalCompletions}
                </ThemedText>
                <MaterialIcons name="check-circle" size={24} color={colors.tint} />
              </View>
            </View>
          </View>
          
          <View style={styles.statRow}>
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>Current Streak</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText type="title" style={styles.statValue}>
                  {currentStreak}
                </ThemedText>
                <MaterialIcons name="local-fire-department" size={24} color="#FF9800" />
              </View>
            </View>
            
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>Longest Streak</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText type="title" style={styles.statValue}>
                  {longestStreak}
                </ThemedText>
                <MaterialIcons name="emoji-events" size={24} color="#FFC107" />
              </View>
            </View>
          </View>
        </ThemedView>
        
        {/* Weekly Chart */}
        <StatisticsChart
          data={weeklyData}
          title="Weekly Progress"
          description="Your habit completion rate for each day of the current week."
        />
        
        {/* Top Habits */}
        <ThemedView style={styles.topHabitsContainer}>
          <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
            Top Performing Habits
          </ThemedText>
          
          {topHabits.map((stat, index) => {
            const habit = habits.find(h => h.id === stat.id);
            if (!habit) return null;
            
            return (
              <View key={stat.id} style={styles.topHabitItem}>
                <View style={styles.topHabitRank}>
                  <ThemedText style={styles.topHabitRankText}>{index + 1}</ThemedText>
                </View>
                
                <View style={styles.topHabitInfo}>
                  <ThemedText type="defaultSemiBold">{habit.name}</ThemedText>
                  <ThemedText style={styles.topHabitSubtext}>
                    {Math.round(stat.completionRate * 100)}% completion rate
                  </ThemedText>
                </View>
                
                <ProgressCircle 
                  progress={stat.completionRate} 
                  size={40} 
                  showPercentage={false} 
                />
              </View>
            );
          })}
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // For status bar
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  title: {
    fontSize: 24,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  statsContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
  },
  statValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    marginRight: 4,
  },
  topHabitsContainer: {
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  topHabitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
  },
  topHabitRank: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topHabitRankText: {
    fontWeight: 'bold',
  },
  topHabitInfo: {
    flex: 1,
  },
  topHabitSubtext: {
    fontSize: 12,
    opacity: 0.7,
  },
});
