import React, { useEffect, useState } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, RefreshControl, TextInput } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { HabitCard } from '@/components/habit/HabitCard';
import { EmptyState } from '@/components/ui/EmptyState';
import { useHabits } from '@/hooks/useHabits';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { HabitCategory } from '@/types/habit';
import { HABIT_CATEGORIES } from '@/constants/HabitCategories';

export default function HabitsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const { 
    habits, 
    loading, 
    error, 
    loadHabits, 
    toggleHabitCompletion, 
    getActiveHabits 
  } = useHabits();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<HabitCategory | 'all'>('all');
  
  // Load habits on mount
  useEffect(() => {
    loadHabits();
  }, [loadHabits]);
  
  // Get active habits
  const activeHabits = getActiveHabits();
  
  // Filter habits based on search and category
  const filteredHabits = activeHabits.filter(habit => {
    const matchesSearch = habit.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (habit.description?.toLowerCase().includes(searchQuery.toLowerCase()) || false);
    
    const matchesCategory = selectedCategory === 'all' || habit.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHabits();
    setRefreshing(false);
  };
  
  // Handle creating a new habit
  const handleCreateHabit = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push('/habit/new');
  };
  
  // Handle toggling habit completion
  const handleToggleCompletion = (habitId: string) => {
    toggleHabitCompletion(habitId);
  };
  
  // Handle category selection
  const handleCategorySelect = (category: HabitCategory | 'all') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(category);
  };
  
  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          My Habits
        </ThemedText>
        
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.tint }]}
          onPress={handleCreateHabit}
        >
          <MaterialIcons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      
      {/* Search Bar */}
      <View style={[styles.searchContainer, { borderColor: colors.tabIconDefault }]}>
        <MaterialIcons name="search" size={24} color={colors.tabIconDefault} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder="Search habits..."
          placeholderTextColor={colors.tabIconDefault}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialIcons name="close" size={24} color={colors.tabIconDefault} />
          </TouchableOpacity>
        ) : null}
      </View>
      
      {/* Category Filter */}
      <View style={styles.categoryFilterContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryList}
        >
          <TouchableOpacity
            style={[
              styles.categoryItem,
              selectedCategory === 'all' && { backgroundColor: colors.tint },
            ]}
            onPress={() => handleCategorySelect('all')}
          >
            <ThemedText
              style={[
                styles.categoryText,
                selectedCategory === 'all' && { color: '#FFFFFF' },
              ]}
            >
              All
            </ThemedText>
          </TouchableOpacity>
          
          {HABIT_CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryItem,
                selectedCategory === category.id && { backgroundColor: category.color },
              ]}
              onPress={() => handleCategorySelect(category.id)}
            >
              <MaterialIcons
                name={category.icon}
                size={16}
                color={selectedCategory === category.id ? '#FFFFFF' : category.color}
                style={styles.categoryIcon}
              />
              <ThemedText
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && { color: '#FFFFFF' },
                ]}
              >
                {category.name}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Habits List */}
      {activeHabits.length > 0 ? (
        <FlatList
          data={filteredHabits}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <HabitCard 
              habit={item} 
              onToggleCompletion={handleToggleCompletion} 
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl 
              refreshing={refreshing} 
              onRefresh={handleRefresh}
              colors={[colors.tint]}
              tintColor={colors.tint}
            />
          }
          ListEmptyComponent={
            <EmptyState
              title="No Matching Habits"
              message="Try adjusting your search or category filter."
              icon="filter-list"
            />
          }
        />
      ) : (
        <EmptyState
          title="No Habits Yet"
          message="Create your first habit to start tracking your progress."
          icon="lightbulb"
          actionLabel="Create Habit"
          onAction={handleCreateHabit}
        />
      )}
    </ThemedView>
  );
}

import { ScrollView } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // For status bar
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  title: {
    fontSize: 24,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 24,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  categoryFilterContainer: {
    marginBottom: 16,
  },
  categoryList: {
    paddingHorizontal: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    backgroundColor: '#F0F0F0',
  },
  categoryIcon: {
    marginRight: 4,
  },
  categoryText: {
    fontSize: 14,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
});
