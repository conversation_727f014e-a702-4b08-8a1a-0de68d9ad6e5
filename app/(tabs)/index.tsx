import React, { useEffect, useState } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, RefreshControl } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { HabitCard } from '@/components/habit/HabitCard';
import { ProgressCircle } from '@/components/habit/ProgressCircle';
import { EmptyState } from '@/components/ui/EmptyState';
import { useHabits } from '@/hooks/useHabits';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

export default function TodayScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const {
    habits,
    loading,
    error,
    loadHabits,
    toggleHabitCompletion,
    getTodayHabits
  } = useHabits();

  const [refreshing, setRefreshing] = useState(false);

  // Load habits on mount
  useEffect(() => {
    loadHabits();
  }, [loadHabits]);

  // Get today's habits
  const todayHabits = getTodayHabits();

  // Calculate completion rate for today
  const completedToday = todayHabits.filter(habit =>
    habit.completions.some(c =>
      c.completed && format(new Date(c.date), 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
    )
  ).length;

  const completionRate = todayHabits.length > 0
    ? completedToday / todayHabits.length
    : 0;

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHabits();
    setRefreshing(false);
  };

  // Handle creating a new habit
  const handleCreateHabit = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push('/habit/new');
  };

  // Handle toggling habit completion
  const handleToggleCompletion = (habitId: string) => {
    toggleHabitCompletion(habitId);
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <ThemedText type="title" style={styles.title}>
            Today's Habits
          </ThemedText>
          <ThemedText style={styles.date}>
            {format(new Date(), 'EEEE, MMMM d')}
          </ThemedText>
        </View>

        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.tint }]}
          onPress={handleCreateHabit}
        >
          <MaterialIcons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Progress Circle */}
      {todayHabits.length > 0 && (
        <View style={styles.progressContainer}>
          <ProgressCircle
            progress={completionRate}
            size={120}
            showPercentage={true}
          />
          <ThemedText style={styles.progressText}>
            {completedToday} of {todayHabits.length} completed
          </ThemedText>
        </View>
      )}

      {/* Habits List */}
      {todayHabits.length > 0 ? (
        <FlatList
          data={todayHabits}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <HabitCard
              habit={item}
              onToggleCompletion={handleToggleCompletion}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.tint]}
              tintColor={colors.tint}
            />
          }
        />
      ) : (
        <EmptyState
          title="No Habits Yet"
          message="Create your first habit to start tracking your progress."
          icon="lightbulb"
          actionLabel="Create Habit"
          onAction={handleCreateHabit}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // For status bar
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  title: {
    fontSize: 24,
  },
  date: {
    fontSize: 16,
    opacity: 0.7,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  progressContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  progressText: {
    marginTop: 8,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
});
