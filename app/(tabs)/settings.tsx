import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Switch, TouchableOpacity, Alert, Linking, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { AppSettings, DEFAULT_SETTINGS, SettingsStorage } from '@/services/storage';
import { NotificationService } from '@/services/notification';

export default function SettingsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(true);
  
  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedSettings = await SettingsStorage.getSettings();
        setSettings(storedSettings);
      } catch (error) {
        console.error('Error loading settings:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadSettings();
  }, []);
  
  // Save settings when they change
  const saveSettings = async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      await SettingsStorage.saveSettings(newSettings);
      setSettings(updatedSettings);
      
      // Update notifications if reminder settings changed
      if (newSettings.reminderEnabled !== undefined || newSettings.reminderTime !== undefined) {
        if (updatedSettings.reminderEnabled && updatedSettings.reminderTime) {
          await NotificationService.scheduleDailyReminder(updatedSettings.reminderTime);
        } else {
          await NotificationService.cancelDailyReminder();
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      return false;
    }
  };
  
  // Handle toggle switches
  const handleToggleSwitch = (key: keyof AppSettings) => async (value: boolean) => {
    if (settings.hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    await saveSettings({ [key]: value });
  };
  
  // Handle reset data
  const handleResetData = () => {
    if (settings.hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Reset All Data',
      'Are you sure you want to reset all data? This will delete all habits and settings.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              // Reset settings
              await SettingsStorage.resetSettings();
              
              // Reset habits (this would be in the HabitStorage service)
              // await HabitStorage.clearHabits();
              
              // Cancel all notifications
              await NotificationService.cancelDailyReminder();
              
              // Reload settings
              const defaultSettings = await SettingsStorage.getSettings();
              setSettings(defaultSettings);
              
              // Show success message
              Alert.alert('Success', 'All data has been reset.');
              
              if (settings.hapticFeedback) {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              }
            } catch (error) {
              console.error('Error resetting data:', error);
              Alert.alert('Error', 'Failed to reset data. Please try again.');
            }
          },
        },
      ]
    );
  };
  
  // Handle opening links
  const handleOpenLink = async (url: string) => {
    if (settings.hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error('Error opening link:', error);
      Alert.alert('Error', 'Could not open the link.');
    }
  };
  
  if (loading) {
    return (
      <ThemedView style={[styles.container, styles.loadingContainer]}>
        <ThemedText>Loading settings...</ThemedText>
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          Settings
        </ThemedText>
      </View>
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Notification Settings */}
        <ThemedView style={styles.section}>
          <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
            Notifications
          </ThemedText>
          
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <ThemedText type="defaultSemiBold">Daily Reminder</ThemedText>
              <ThemedText>Receive a daily reminder to complete your habits</ThemedText>
            </View>
            <Switch
              value={settings.reminderEnabled}
              onValueChange={handleToggleSwitch('reminderEnabled')}
              trackColor={{ false: '#767577', true: colors.tint }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          {settings.reminderEnabled && (
            <View style={styles.settingItem}>
              <View style={styles.settingTextContainer}>
                <ThemedText type="defaultSemiBold">Reminder Time</ThemedText>
                <ThemedText>Set the time for your daily reminder</ThemedText>
              </View>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => {
                  // In a real app, this would open a time picker
                  // For now, we'll just toggle between a few preset times
                  const times = ['08:00', '12:00', '16:00', '20:00'];
                  const currentIndex = times.indexOf(settings.reminderTime || '20:00');
                  const nextIndex = (currentIndex + 1) % times.length;
                  saveSettings({ reminderTime: times[nextIndex] });
                  
                  if (settings.hapticFeedback) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                }}
              >
                <ThemedText style={styles.timeButtonText}>
                  {settings.reminderTime || '20:00'}
                </ThemedText>
              </TouchableOpacity>
            </View>
          )}
        </ThemedView>
        
        {/* Appearance Settings */}
        <ThemedView style={styles.section}>
          <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
            Appearance
          </ThemedText>
          
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <ThemedText type="defaultSemiBold">Dark Mode</ThemedText>
              <ThemedText>Use dark theme</ThemedText>
            </View>
            <ThemedText>System</ThemedText>
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <ThemedText type="defaultSemiBold">Haptic Feedback</ThemedText>
              <ThemedText>Enable vibration feedback on actions</ThemedText>
            </View>
            <Switch
              value={settings.hapticFeedback}
              onValueChange={handleToggleSwitch('hapticFeedback')}
              trackColor={{ false: '#767577', true: colors.tint }}
              thumbColor="#f4f3f4"
            />
          </View>
        </ThemedView>
        
        {/* Data Management */}
        <ThemedView style={styles.section}>
          <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
            Data Management
          </ThemedText>
          
          <TouchableOpacity
            style={styles.dangerButton}
            onPress={handleResetData}
          >
            <MaterialIcons name="delete" size={20} color="#FF5252" />
            <ThemedText style={styles.dangerButtonText}>Reset All Data</ThemedText>
          </TouchableOpacity>
        </ThemedView>
        
        {/* About */}
        <ThemedView style={styles.section}>
          <ThemedText type="defaultSemiBold" style={styles.sectionTitle}>
            About
          </ThemedText>
          
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => handleOpenLink('https://github.com/yourusername/mindbox')}
          >
            <MaterialIcons name="code" size={20} color={colors.tint} />
            <ThemedText style={[styles.linkButtonText, { color: colors.tint }]}>
              Source Code
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => handleOpenLink('https://github.com/yourusername/mindbox/issues')}
          >
            <MaterialIcons name="bug-report" size={20} color={colors.tint} />
            <ThemedText style={[styles.linkButtonText, { color: colors.tint }]}>
              Report an Issue
            </ThemedText>
          </TouchableOpacity>
          
          <View style={styles.versionContainer}>
            <ThemedText style={styles.versionText}>Version 1.0.0</ThemedText>
          </View>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // For status bar
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  title: {
    fontSize: 24,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#CCCCCC',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  timeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#F0F0F0',
  },
  timeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  dangerButtonText: {
    color: '#FF5252',
    marginLeft: 8,
    fontWeight: '500',
  },
  linkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#CCCCCC',
  },
  linkButtonText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  versionContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  versionText: {
    fontSize: 14,
    opacity: 0.6,
  },
});
