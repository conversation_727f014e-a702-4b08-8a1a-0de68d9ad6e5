import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Platform,
  Alert,
  Animated
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

// Mock data for past entries
const PAST_ENTRIES = [
  {
    id: '1',
    text: 'The warm sunshine on my face this morning. It felt so good after a week of rain.',
    date: 'Oct 26',
    feeling: 'Joyful',
    feelingIcon: 'sentiment-very-satisfied',
    feelingColor: '#F59E0B' // Yellow
  },
  {
    id: '2',
    text: 'A productive work day and getting through my to-do list. It\'s satisfying to accomplish tasks.',
    date: 'Oct 25',
    feeling: 'Accomplished',
    feelingIcon: 'verified',
    feelingColor: '#10B981' // Green
  },
  {
    id: '3',
    text: 'My family. They always know how to make me smile, even on tough days.',
    date: 'Oct 24',
    feeling: 'Loved',
    feelingIcon: 'favorite',
    feelingColor: '#EF4444' // Red
  }
];

export default function GratitudeScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const [gratitudeText, setGratitudeText] = useState('');
  const [entries, setEntries] = useState(PAST_ENTRIES);
  const [fadeAnims] = useState(() =>
    PAST_ENTRIES.map(() => new Animated.Value(0))
  );

  // Animate entries when component mounts
  useEffect(() => {
    fadeAnims.forEach((anim, index) => {
      Animated.timing(anim, {
        toValue: 1,
        duration: 500,
        delay: 100 * (index + 1),
        useNativeDriver: true,
      }).start();
    });
  }, []);

  const handleClear = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setGratitudeText('');
  };

  const handleSave = () => {
    if (!gratitudeText.trim()) {
      Alert.alert("Empty Entry", "Please write something you're grateful for.");
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Create a new entry
    const newEntry = {
      id: Date.now().toString(),
      text: gratitudeText,
      date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      feeling: 'Grateful',
      feelingIcon: 'favorite',
      feelingColor: '#EF4444'
    };

    // Add the new entry to the beginning of the list
    setEntries([newEntry, ...entries.slice(0, 2)]);
    setGratitudeText('');

    // Show success message
    Alert.alert("Entry Saved", "Your gratitude entry has been saved.");
  };

  const renderEntryCard = (entry, index) => {
    const animStyle = {
      opacity: fadeAnims[index] || 1,
      transform: [{
        translateY: fadeAnims[index]?.interpolate({
          inputRange: [0, 1],
          outputRange: [20, 0],
        }) || 0,
      }],
    };

    return (
      <Animated.View key={entry.id} style={[styles.entryCard, animStyle]}>
        <View style={styles.entryHeader}>
          <Text style={styles.entryText}>{entry.text}</Text>
          <Text style={styles.entryDate}>{entry.date}</Text>
        </View>
        <View style={styles.entryFooter}>
          <MaterialIcons name={entry.feelingIcon} size={16} color={entry.feelingColor} />
          <Text style={styles.feelingText}>Feeling: {entry.feeling}</Text>
        </View>
      </Animated.View>
    );
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <SafeAreaView style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <MaterialIcons name="arrow-back" size={24} color="#4A5568" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Gratitude Journal</Text>
          <View style={styles.headerRight} />
        </View>
      </SafeAreaView>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Gratitude Input Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What are you grateful for today?</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Today, I am grateful for..."
              placeholderTextColor="#A0AEC0"
              multiline
              value={gratitudeText}
              onChangeText={setGratitudeText}
            />
          </View>
          <Text style={styles.limitText}>You can add up to 3 entries per day.</Text>
        </View>

        {/* Past Entries Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Past Entries</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.entriesContainer}>
            {entries.map((entry, index) => renderEntryCard(entry, index))}
          </View>
        </View>

        {/* Reminder Section */}
        <View style={styles.reminderContainer}>
          <MaterialIcons name="psychology" size={30} color="#738FAA" />
          <Text style={styles.reminderTitle}>Remember your progress!</Text>
          <Text style={styles.reminderText}>
            AI will send you an uplifting reminder tomorrow to continue your gratitude journey.
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <SafeAreaView style={styles.footer}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
          >
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.saveButton,
              !gratitudeText.trim() && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={!gratitudeText.trim()}
          >
            <Text style={styles.saveButtonText}>Save Entry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(226, 232, 240, 0.8)',
    paddingTop: Platform.OS === 'ios' ? 44 : 20,
    backdropFilter: 'blur(10px)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'relative',
    height: 56,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: 8,
    zIndex: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A202C',
    textAlign: 'center',
    flex: 1,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  headerRight: {
    width: 40,
    opacity: 0, // Make invisible but keep for layout
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#4A5568',
    marginBottom: 16,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8FA9C1',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(226, 232, 240, 0.8)',
  },
  input: {
    padding: 16,
    fontSize: 16,
    color: '#2D3748',
    minHeight: 140,
    textAlignVertical: 'top',
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  limitText: {
    fontSize: 12,
    color: '#718096',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  entriesContainer: {
    gap: 20,
  },
  entryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(226, 232, 240, 0.8)',
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  entryText: {
    fontSize: 14,
    color: '#4A5568',
    flex: 1,
    fontStyle: 'italic',
    lineHeight: 20,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  entryDate: {
    fontSize: 12,
    color: '#A0AEC0',
    marginLeft: 8,
    paddingTop: 2,
  },
  entryFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  feelingText: {
    fontSize: 12,
    color: '#718096',
  },
  reminderContainer: {
    backgroundColor: '#e6eff9',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(193, 210, 230, 0.7)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A6A8A',
    marginTop: 8,
    marginBottom: 4,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  reminderText: {
    fontSize: 14,
    color: '#5B7E9F',
    textAlign: 'center',
    lineHeight: 20,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(226, 232, 240, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20, // Account for bottom safe area
    backdropFilter: 'blur(10px)',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 16,
  },
  clearButton: {
    backgroundColor: 'rgba(237, 242, 247, 0.9)',
    borderRadius: 12,
    height: 48,
    minWidth: 130,
    maxWidth: 130,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  clearButtonText: {
    color: '#4A5568',
    fontWeight: '500',
    fontSize: 14,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#A7BED3',
    borderRadius: 12,
    height: 48,
    minWidth: 130,
    maxWidth: 130,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#A7BED3',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 3,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: '#1E293B',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center',
  },
});
