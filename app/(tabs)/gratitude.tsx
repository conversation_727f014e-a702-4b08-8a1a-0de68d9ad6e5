import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Platform,
  Alert,
  Animated
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

// Mock data for past entries
const PAST_ENTRIES = [
  {
    id: '1',
    text: 'The warm sunshine on my face this morning. It felt so good after a week of rain.',
    date: 'Oct 26',
    feeling: 'Joyful',
    feelingIcon: 'sentiment-very-satisfied',
    feelingColor: '#F59E0B' // Yellow
  },
  {
    id: '2',
    text: 'A productive work day and getting through my to-do list. It\'s satisfying to accomplish tasks.',
    date: 'Oct 25',
    feeling: 'Accomplished',
    feelingIcon: 'verified',
    feelingColor: '#10B981' // Green
  },
  {
    id: '3',
    text: 'My family. They always know how to make me smile, even on tough days.',
    date: 'Oct 24',
    feeling: 'Loved',
    feelingIcon: 'favorite',
    feelingColor: '#EF4444' // Red
  }
];

export default function GratitudeScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const [gratitudeText, setGratitudeText] = useState('');
  const [entries, setEntries] = useState(PAST_ENTRIES);
  const [fadeAnims] = useState(() =>
    PAST_ENTRIES.map(() => new Animated.Value(0))
  );

  // Animate entries when component mounts
  useEffect(() => {
    fadeAnims.forEach((anim, index) => {
      Animated.timing(anim, {
        toValue: 1,
        duration: 500,
        delay: 100 * (index + 1),
        useNativeDriver: true,
      }).start();
    });
  }, []);

  const handleClear = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setGratitudeText('');
  };

  const handleSave = () => {
    if (!gratitudeText.trim()) {
      Alert.alert("Empty Entry", "Please write something you're grateful for.");
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Create a new entry
    const newEntry = {
      id: Date.now().toString(),
      text: gratitudeText,
      date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      feeling: 'Grateful',
      feelingIcon: 'favorite',
      feelingColor: '#EF4444'
    };

    // Add the new entry to the beginning of the list
    setEntries([newEntry, ...entries.slice(0, 2)]);
    setGratitudeText('');

    // Show success message
    Alert.alert("Entry Saved", "Your gratitude entry has been saved.");
  };

  const renderEntryCard = (entry, index) => {
    const animStyle = {
      opacity: fadeAnims[index] || 1,
      transform: [{
        translateY: fadeAnims[index]?.interpolate({
          inputRange: [0, 1],
          outputRange: [20, 0],
        }) || 0,
      }],
    };

    // Add hover effect with scale
    const onPressIn = () => {
      Animated.spring(fadeAnims[index], {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    };

    const onPressOut = () => {
      Animated.spring(fadeAnims[index], {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View
        key={entry.id}
        style={[styles.entryCard, animStyle, { marginBottom: 20 }]}
      >
        <View style={{ gap: 12 }}>
          <View style={styles.entryHeader}>
            <Text style={styles.entryText}>{entry.text}</Text>
            <Text style={styles.entryDate}>{entry.date}</Text>
          </View>
          <View style={styles.entryFooter}>
            <MaterialIcons name={entry.feelingIcon} size={16} color={entry.feelingColor} />
            <Text style={styles.feelingText}>Feeling: {entry.feeling}</Text>
          </View>
        </View>
      </Animated.View>
    );
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <SafeAreaView style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <MaterialIcons name="arrow-back-ios-new" size={22} color="#4B5563" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Gratitude Journal</Text>
          <View style={styles.headerRight} />
        </View>
      </SafeAreaView>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Gratitude Input Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What are you grateful for today?</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Today, I am grateful for..."
              placeholderTextColor="#A0AEC0"
              multiline
              value={gratitudeText}
              onChangeText={setGratitudeText}
            />
          </View>
          <Text style={styles.limitText}>You can add up to 3 entries per day.</Text>
        </View>

        {/* Past Entries Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Past Entries</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.entriesContainer}>
            {entries.map((entry, index) => renderEntryCard(entry, index))}
          </View>
        </View>

        {/* Reminder Section */}
        <View style={styles.reminderContainer}>
          <MaterialIcons name="psychology" size={32} color="#738FAA" style={{ marginBottom: 4 }} />
          <Text style={styles.reminderTitle}>Remember your progress!</Text>
          <Text style={styles.reminderText}>
            AI will send you an uplifting reminder tomorrow to continue your gratitude journey.
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <SafeAreaView style={styles.footer}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
          >
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.saveButton,
              !gratitudeText.trim() && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={!gratitudeText.trim()}
          >
            <Text style={styles.saveButtonText}>Save Entry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fdfbfb',
  },
  header: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(226, 232, 240, 0.8)',
    paddingTop: Platform.OS === 'ios' ? 44 : 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 12,
    position: 'relative',
  },
  backButton: {
    width: 40, // size-10
    height: 40,
    borderRadius: 20, // rounded-full
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: 8,
    zIndex: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937', // text-gray-800
    textAlign: 'center',
    flex: 1,
    paddingRight: 40, // pr-10
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
    lineHeight: 24,
    letterSpacing: -0.5, // tracking-tight
  },
  headerRight: {
    width: 40,
    opacity: 0, // Make invisible but keep for layout
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
    paddingTop: 32, // py-8
  },
  section: {
    marginBottom: 32, // space-y-8
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20, // space-y-5
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#374151', // text-gray-700
    marginBottom: 16,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8FA9C1', // text-[#8FA9C1]
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12, // rounded-xl
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5, // shadow-lg
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(229, 231, 235, 0.8)', // border-gray-200/80
  },
  input: {
    padding: 20, // p-5
    fontSize: 16, // text-base
    color: '#374151', // text-gray-700
    minHeight: 140, // min-h-[140px]
    textAlignVertical: 'top',
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
    lineHeight: 24, // leading-relaxed
  },
  limitText: {
    fontSize: 12, // text-xs
    color: '#6B7280', // text-gray-500
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  entriesContainer: {
    gap: 20, // gap-5
  },
  entryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12, // rounded-xl
    padding: 20, // p-5
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5, // shadow-lg
    borderWidth: 1,
    borderColor: 'rgba(229, 231, 235, 0.8)', // border-gray-200/80
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start', // items-start
    marginBottom: 12,
  },
  entryText: {
    fontSize: 14, // text-sm
    color: '#374151', // text-gray-700
    flex: 1,
    fontStyle: 'italic',
    lineHeight: 20, // leading-relaxed
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  entryDate: {
    fontSize: 12, // text-xs
    color: '#9CA3AF', // text-gray-400
    marginLeft: 12, // pl-3
    paddingTop: 2, // pt-0.5
    flexShrink: 0, // whitespace-nowrap
  },
  entryFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6, // gap-1.5
  },
  feelingText: {
    fontSize: 12, // text-xs
    color: '#4B5563', // text-gray-600
  },
  reminderContainer: {
    backgroundColor: '#e6eff9', // from-[#e6eff9]
    borderRadius: 12, // rounded-xl
    padding: 20, // p-5
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(193, 210, 230, 0.7)', // border-[#C1D2E6]/70
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 3,
    elevation: 2, // shadow-md
    gap: 8, // space-y-2
  },
  reminderTitle: {
    fontSize: 14, // text-sm
    fontWeight: '500', // font-medium
    color: '#4A6A8A', // text-[#4A6A8A]
    marginTop: 0,
    marginBottom: 0,
    fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
  },
  reminderText: {
    fontSize: 12, // text-xs
    color: '#5B7E9F', // text-[#5B7E9F]
    textAlign: 'center',
    lineHeight: 16,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // bg-white/70
    borderTopWidth: 1,
    borderTopColor: 'rgba(229, 231, 235, 0.8)', // border-gray-200/80
    padding: 16, // p-4
    paddingBottom: Platform.OS === 'ios' ? 32 : 16, // Account for bottom safe area
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 16, // gap-4
  },
  clearButton: {
    backgroundColor: 'rgba(229, 231, 235, 0.9)', // bg-gray-200/90
    borderRadius: 12, // rounded-xl
    height: 48, // h-12
    minWidth: 130, // max-w-[130px]
    maxWidth: 130,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1, // flex-1
    paddingHorizontal: 16, // px-4
  },
  clearButtonText: {
    color: '#374151', // text-gray-700
    fontWeight: '500', // font-medium
    fontSize: 14, // text-sm
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#A7BED3', // bg-[#A7BED3]
    borderRadius: 12, // rounded-xl
    height: 48, // h-12
    minWidth: 130, // max-w-[130px]
    maxWidth: 130,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1, // flex-1
    paddingHorizontal: 16, // px-4
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2, // shadow-md
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: '#1F2937', // text-gray-800
    fontWeight: '600', // font-semibold
    fontSize: 14, // text-sm
    textAlign: 'center',
  },
});
