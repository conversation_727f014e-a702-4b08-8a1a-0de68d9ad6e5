import AsyncStorage from '@react-native-async-storage/async-storage';
import { Habit } from '@/types/habit';

// Storage keys
const HABITS_STORAGE_KEY = 'mindbox_habits';
const SETTINGS_STORAGE_KEY = 'mindbox_settings';

// Storage service for habits
export const HabitStorage = {
  // Get all habits
  getHabits: async (): Promise<Habit[]> => {
    try {
      const habitsJson = await AsyncStorage.getItem(HABITS_STORAGE_KEY);
      return habitsJson ? JSON.parse(habitsJson) : [];
    } catch (error) {
      console.error('Error getting habits:', error);
      return [];
    }
  },

  // Get a single habit by ID
  getHabit: async (id: string): Promise<Habit | null> => {
    try {
      const habits = await HabitStorage.getHabits();
      return habits.find(habit => habit.id === id) || null;
    } catch (error) {
      console.error('Error getting habit:', error);
      return null;
    }
  },

  // Save a new habit
  saveHabit: async (habit: Habit): Promise<boolean> => {
    try {
      const habits = await HabitStorage.getHabits();
      const updatedHabits = [...habits, habit];
      await AsyncStorage.setItem(HABITS_STORAGE_KEY, JSON.stringify(updatedHabits));
      return true;
    } catch (error) {
      console.error('Error saving habit:', error);
      return false;
    }
  },

  // Update an existing habit
  updateHabit: async (updatedHabit: Habit): Promise<boolean> => {
    try {
      const habits = await HabitStorage.getHabits();
      const updatedHabits = habits.map(habit => 
        habit.id === updatedHabit.id ? updatedHabit : habit
      );
      await AsyncStorage.setItem(HABITS_STORAGE_KEY, JSON.stringify(updatedHabits));
      return true;
    } catch (error) {
      console.error('Error updating habit:', error);
      return false;
    }
  },

  // Delete a habit
  deleteHabit: async (id: string): Promise<boolean> => {
    try {
      const habits = await HabitStorage.getHabits();
      const updatedHabits = habits.filter(habit => habit.id !== id);
      await AsyncStorage.setItem(HABITS_STORAGE_KEY, JSON.stringify(updatedHabits));
      return true;
    } catch (error) {
      console.error('Error deleting habit:', error);
      return false;
    }
  },

  // Archive a habit
  archiveHabit: async (id: string): Promise<boolean> => {
    try {
      const habits = await HabitStorage.getHabits();
      const updatedHabits = habits.map(habit => 
        habit.id === id ? { ...habit, archived: true } : habit
      );
      await AsyncStorage.setItem(HABITS_STORAGE_KEY, JSON.stringify(updatedHabits));
      return true;
    } catch (error) {
      console.error('Error archiving habit:', error);
      return false;
    }
  },

  // Clear all habits (for testing/reset)
  clearHabits: async (): Promise<boolean> => {
    try {
      await AsyncStorage.removeItem(HABITS_STORAGE_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing habits:', error);
      return false;
    }
  }
};

// Settings interface
export interface AppSettings {
  darkMode: 'system' | 'light' | 'dark';
  reminderTime: string | null; // HH:MM format
  reminderEnabled: boolean;
  hapticFeedback: boolean;
  firstLaunch: boolean;
}

// Default settings
export const DEFAULT_SETTINGS: AppSettings = {
  darkMode: 'system',
  reminderTime: '20:00', // 8:00 PM
  reminderEnabled: true,
  hapticFeedback: true,
  firstLaunch: true,
};

// Storage service for app settings
export const SettingsStorage = {
  // Get settings
  getSettings: async (): Promise<AppSettings> => {
    try {
      const settingsJson = await AsyncStorage.getItem(SETTINGS_STORAGE_KEY);
      return settingsJson ? { ...DEFAULT_SETTINGS, ...JSON.parse(settingsJson) } : DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Error getting settings:', error);
      return DEFAULT_SETTINGS;
    }
  },

  // Save settings
  saveSettings: async (settings: Partial<AppSettings>): Promise<boolean> => {
    try {
      const currentSettings = await SettingsStorage.getSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await AsyncStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      return false;
    }
  },

  // Reset settings to default
  resetSettings: async (): Promise<boolean> => {
    try {
      await AsyncStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
      return true;
    } catch (error) {
      console.error('Error resetting settings:', error);
      return false;
    }
  }
};
