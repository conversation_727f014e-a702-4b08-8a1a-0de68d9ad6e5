import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { Habit } from '@/types/habit';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export const NotificationService = {
  // Request permissions
  requestPermissions: async (): Promise<boolean> => {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();

    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Notification permission not granted');
      return false;
    }

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('habits', {
        name: 'Habit Reminders',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    return true;
  },

  // Schedule a daily reminder for a habit
  scheduleHabitReminder: async (habit: Habit): Promise<string | null> => {
    if (!habit.reminderTime) return null;

    try {
      const hasPermission = await NotificationService.requestPermissions();
      if (!hasPermission) return null;

      // Parse reminder time (HH:MM)
      const [hours, minutes] = habit.reminderTime.split(':').map(Number);

      // Cancel any existing notifications for this habit
      await NotificationService.cancelHabitReminder(habit.id);

      // Create trigger for daily notification at specified time
      const trigger = {
        hour: hours,
        minute: minutes,
        repeats: true,
      };

      // Schedule the notification
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Habit Reminder',
          body: `Time to complete your habit: ${habit.name}`,
          data: { habitId: habit.id },
        },
        trigger,
      });

      return identifier;
    } catch (error) {
      console.error('Error scheduling habit reminder:', error);
      return null;
    }
  },

  // Cancel a habit reminder
  cancelHabitReminder: async (habitId: string): Promise<void> => {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();

      for (const notification of scheduledNotifications) {
        if (notification.content.data?.habitId === habitId) {
          await Notifications.cancelScheduledNotificationAsync(notification.identifier);
        }
      }
    } catch (error) {
      console.error('Error canceling habit reminder:', error);
    }
  },

  // Schedule a general daily reminder
  scheduleDailyReminder: async (time: string): Promise<string | null> => {
    try {
      const hasPermission = await NotificationService.requestPermissions();
      if (!hasPermission) return null;

      // Parse reminder time (HH:MM)
      const [hours, minutes] = time.split(':').map(Number);

      // Cancel any existing general reminders
      await NotificationService.cancelDailyReminder();

      // Create trigger for daily notification at specified time
      const trigger = {
        hour: hours,
        minute: minutes,
        repeats: true,
      };

      // Schedule the notification
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Daily Habits Reminder',
          body: 'Don\'t forget to check in with your habits today!',
          data: { type: 'daily-reminder' },
        },
        trigger,
      });

      return identifier;
    } catch (error) {
      console.error('Error scheduling daily reminder:', error);
      return null;
    }
  },

  // Cancel the general daily reminder
  cancelDailyReminder: async (): Promise<void> => {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();

      for (const notification of scheduledNotifications) {
        if (notification.content.data?.type === 'daily-reminder') {
          await Notifications.cancelScheduledNotificationAsync(notification.identifier);
        }
      }
    } catch (error) {
      console.error('Error canceling daily reminder:', error);
    }
  },
};
